# 小学乘法练习题生成器

这是一个Vue.js应用程序，用于生成小学乘法练习题并导出为PDF。

## 功能特点

### 🆕 多天题目生成
- 可以输入天数（1-30天）
- 点击"生成X天新题目"按钮生成多天的练习题
- 每天包含5道不同的乘法题目
- 每页布局完全一致，仅题目内容不同

### 📄 PDF导出
- 支持多页PDF导出
- 每页包含5道题目
- 自动分页，确保每天的题目在独立页面上
- PDF文件名包含天数和日期信息

### 🔍 二维码答案
- 每页右上角都有独立的二维码
- 扫码可查看该页所有题目的答案
- 二维码内容格式：第X天答案 + 题目答案列表

## 使用方法

1. **设置天数**：在左上角的输入框中输入要生成的天数（1-30）
2. **生成题目**：点击"生成X天新题目"按钮
3. **预览题目**：在页面上查看生成的所有天的题目
4. **导出PDF**：点击"导出PDF"按钮下载多页PDF文件

## 项目设置

### 安装依赖
```
npm install
# 或
yarn install
```

### 开发环境运行
```
npm run serve
# 或
yarn serve
```

### 生产环境构建
```
npm run build
# 或
yarn build
```

### 代码检查
```
npm run lint
# 或
yarn lint
```

## 更新内容

### v2.0 - 多天题目支持
- ✅ 将"生成新题目"改为"生成X天新题目"
- ✅ 添加天数输入控件
- ✅ 支持生成多页PDF，每页5题
- ✅ 每页布局与原始设计完全一致
- ✅ 每页独立的二维码答案
- ✅ 自动分页和页面标识

### 技术实现
- **前端框架**：Vue 3
- **PDF生成**：html2pdf.js
- **二维码生成**：qrcode
- **样式**：CSS3 with print media queries

### 配置参考
See [Configuration Reference](https://cli.vuejs.org/config/).
