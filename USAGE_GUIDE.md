# 使用指南

## 新功能说明

### 1. 多天题目生成
原来的"生成新题目"按钮已经升级为"生成X天新题目"功能：

- **输入天数**：在左上角控制面板的输入框中输入1-30之间的数字
- **动态按钮文本**：按钮文本会根据输入的天数动态变化，例如"生成3天新题目"
- **生成题目**：点击按钮后会生成指定天数的练习题

### 2. 多页PDF布局
每天的题目都会在独立的页面上显示：

- **页面标识**：每页标题显示"一、列竖式计算 (第X天)"
- **独立布局**：每页都有完整的标题、姓名栏、日期栏、用时栏
- **自动分页**：PDF导出时会自动分页，确保每天的题目在单独页面

### 3. 独立二维码
每页都有自己的二维码：

- **位置**：右上角固定位置
- **内容**：包含该页所有题目的答案
- **格式**：第X天答案 + 题目编号和答案列表

## 操作步骤

### 步骤1：设置天数
1. 在页面左上角找到控制面板
2. 在"天数:"输入框中输入想要生成的天数（1-30）
3. 注意按钮文本会实时更新显示当前设置的天数

### 步骤2：生成题目
1. 点击"生成X天新题目"按钮
2. 系统会自动生成指定天数的练习题
3. 每天包含5道不同的三位数乘法题目
4. 页面会显示所有天的题目，可以滚动查看

### 步骤3：预览和检查
1. 滚动页面查看所有生成的题目
2. 每页都有独立的标题和二维码
3. 确认题目数量和布局是否正确

### 步骤4：导出PDF
1. 点击"导出PDF"按钮
2. 系统会生成多页PDF文件
3. 文件名格式：`小学乘法练习_X天_日期.pdf`
4. 每页自动分页，保持原有布局

## 技术细节

### 题目生成规则
- 每道题目都是三位数乘法（100-999）
- 使用随机数生成，确保每次都不同
- 每天5道题目，题目之间相互独立

### PDF分页机制
- 使用CSS `page-break-before: always` 实现强制分页
- 每页内容使用 `page-break-inside: avoid` 避免内容被分割
- 保持原有的瀑布流布局和样式

### 二维码生成
- 使用qrcode库生成二维码
- 每页独立生成，包含该页所有答案
- 二维码尺寸：120x120像素，适合扫描

## 兼容性说明

### 保持向后兼容
- 原有的单页功能仍然可用（设置天数为1）
- 所有原有的样式和布局保持不变
- 导出功能完全兼容原有格式

### 浏览器支持
- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 需要支持ES6+语法
- 建议使用最新版本浏览器以获得最佳体验

## 常见问题

### Q: 最多可以生成多少天的题目？
A: 目前限制为30天，这是为了确保PDF文件大小合理和生成速度。

### Q: 二维码扫描不出来怎么办？
A: 确保二维码清晰可见，如果PDF导出质量较低，可以尝试调整导出设置。

### Q: 可以修改每天的题目数量吗？
A: 目前固定为每天5道题目，这与原有设计保持一致。

### Q: PDF文件太大怎么办？
A: 可以减少生成的天数，或者分批生成多个较小的PDF文件。
