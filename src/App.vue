<template>
  <!-- 顶部一个生成按钮,每次点击都会生成不同的五个题目,以及对应的二维码答案.
  以下内容题目内容,需要可以被到处成pdf. -->

  <!-- 控制按钮区域 -->
  <div class="control-panel">
    <div class="input-group">
      <label for="days-input">天数:</label>
      <input
        id="days-input"
        v-model.number="daysCount"
        type="number"
        min="1"
        max="30"
        class="days-input"
        placeholder="输入天数"
      />
      <button @click="generateMultipleDaysProblems" class="generate-btn">生成{{ daysCount }}天新题目</button>
    </div>
    <button @click="exportToPDF" class="export-btn">导出PDF</button>
  </div>

  <!-- 多页PDF内容 -->
  <div id="pdf-content">
    <div
      v-for="(dayProblems, dayIndex) in allDaysProblems"
      :key="dayIndex"
      class="problem page"
      :class="{ 'page-break': dayIndex > 0 }"
    >
      <div class="title">
        <div class="centered-content">
          <h1>小学乘法练习</h1>
          <span>姓名: _____________ 日期: _____________ 用时: _____________</span><br><br>
        </div>
        <span>一、列竖式计算 (第{{ dayIndex + 1 }}天)</span><br>
      </div>

      <!-- 页面右上角要显示一个二维码,扫码可以看到 所有题目的答案. -->
      <div class="qr-code-container">
        <canvas :ref="`qrCanvas${dayIndex}`" class="qr-code"></canvas>
        <span class="qr-label">扫码查看答案</span>
      </div>

      <div class="waterfall-container">
        <Multiplication v-for="(item, index) in dayProblems" :key="`${dayIndex}-${index}`" :first="item.first" :second="item.second" />
      </div>
    </div>
  </div>
</template>

<script>
  import Multiplication from './components/Multiplication.vue';
  import QRCode from 'qrcode';
  import html2pdf from 'html2pdf.js';

  export default {
    components: { Multiplication },
    data() {
      return {
        daysCount: 1, // 默认生成1天的题目
        allDaysProblems: [], // 存储所有天的题目
        problems: [] // 保持兼容性，暂时保留
      };
    },
    mounted() {
      this.generateMultipleDaysProblems();
    },
    methods: {
      // 生成单天的随机乘法题目
      generateSingleDayProblems() {
        const problems = [];
        for (let i = 0; i < 5; i++) {
          const first = Math.floor(Math.random() * 900) + 100; // 100-999
          const second = Math.floor(Math.random() * 900) + 100; // 100-999
          problems.push({ first, second });
        }
        return problems;
      },

      // 生成多天的题目
      generateMultipleDaysProblems() {
        this.allDaysProblems = [];
        for (let day = 0; day < this.daysCount; day++) {
          this.allDaysProblems.push(this.generateSingleDayProblems());
        }
        // 重新生成所有二维码
        this.$nextTick(() => {
          this.generateAllQRCodes();
        });
      },

      // 保持兼容性的方法
      generateNewProblems() {
        this.daysCount = 1;
        this.generateMultipleDaysProblems();
      },

      // 生成所有天的二维码
      generateAllQRCodes() {
        this.allDaysProblems.forEach((dayProblems, dayIndex) => {
          this.generateQRCodeForDay(dayProblems, dayIndex);
        });
      },

      // 生成单天的二维码
      generateQRCodeForDay(dayProblems, dayIndex) {
        const answers = dayProblems.map((problem, i) => {
          return `${i+1}: ${problem.first * problem.second}`;
        }).join(' \n | ');

        const qrText = `\n${answers}`;
        console.log(`Day ${dayIndex + 1} QR Text:`, qrText);

        // 使用动态ref获取canvas元素
        this.$nextTick(() => {
          const canvas = this.$refs[`qrCanvas${dayIndex}`];
          if (canvas) {
            // 在Vue 3中，单个元素的ref直接返回元素，不是数组
            const canvasElement = Array.isArray(canvas) ? canvas[0] : canvas;
            if (canvasElement) {
              QRCode.toCanvas(canvasElement, qrText, {
                width: 120,
                margin: 1,
                color: {
                  dark: '#000000',
                  light: '#FFFFFF'
                }
              }, (error) => {
                if (error) console.error(`QR Code generation failed for day ${dayIndex + 1}:`, error);
              });
            }
          }
        });
      },

      // 保持兼容性的方法
      generateQRCode() {
        if (this.allDaysProblems.length > 0) {
          this.generateQRCodeForDay(this.allDaysProblems[0], 0);
        }
      },

      // 导出为PDF
      exportToPDF() {
        const element = document.getElementById('pdf-content');
        const opt = {
          margin: 0.1,
          filename: `小学乘法练习_${this.daysCount}天_${new Date().toLocaleDateString()}.pdf`,
          image: { type: 'jpeg', quality: 1 },
          html2canvas: {
            scale: 2, // 适中的缩放比例
            useCORS: true,
            allowTaint: true,
            width: 800 
          },
          jsPDF: {
            unit: 'in',
            format: 'letter',
            orientation: 'portrait'
          },
          pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
        };

        html2pdf().set(opt).from(element).save();
      }
    }
  }
</script>

<style scoped>
  .control-panel {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: white;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }

  .input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .input-group label {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }

  .days-input {
    width: 80px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }

  .generate-btn, .export-btn {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
  }

  .generate-btn:hover, .export-btn:hover {
    background-color: #0056b3;
  }

  .problem {
    position: relative;
    padding-top: 30px; /* 为顶部按钮留出空间 */
    min-height: 100vh; /* 确保每页至少占满一屏 */
    page-break-inside: avoid; /* 避免页面内容被分割 */
  }

  .page {
    width: 100%;
    box-sizing: border-box;
  }

  .page-break {
    page-break-before: always; /* 强制分页 */
  }

  .title {
    margin-left: 20px;
  }

  .centered-content {
    text-align: center;
  }

  .qr-code-container {
    position: absolute;
    top: 6px;
    right: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px; 
  }

  .qr-code {
    margin-bottom: 5px;
  }

  .qr-label {
    font-size: 12px;
    color: #666;
    text-align: center;
  }

  .waterfall-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: 6px;
  }

  .waterfall-container>* {
    break-inside: avoid;
   
  }

  /* 打印时隐藏控制按钮 */
  @media print {
    .control-panel {
      display: none;
    }
    .problem {
      padding-top: 0;
    }
    .page-break {
      page-break-before: always;
    }
    .page {
      page-break-inside: avoid;
    }
  }
</style>